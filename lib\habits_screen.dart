import 'package:flutter/material.dart';
import 'habit.dart';
import 'habit_tile.dart';
import 'date_scroller.dart';

class HabitsScreen extends StatefulWidget {
  const HabitsScreen({super.key});

  @override
  State<HabitsScreen> createState() => _HabitsScreenState();
}

class _HabitsScreenState extends State<HabitsScreen> {
  final ScrollController _scrollController = ScrollController();

  // Hardcoded temporary list of habits with some sample data
  List<Habit> habits = [
    Habit(name: "Read for 15 minutes"),
    Habit(name: "Drink 8 glasses of water"),
    Habit(name: "Exercise for 30 minutes"),
  ];

  @override
  void initState() {
    super.initState();
    _initializeSampleData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeSampleData() {
    // Add some sample completion data for demonstration
    final today = DateTime.now();
    final yesterday = today.subtract(const Duration(days: 1));
    final twoDaysAgo = today.subtract(const Duration(days: 2));

    // Sample data for "Read for 15 minutes"
    habits[0].setCompletionForDate(today, true);
    habits[0].setCompletionForDate(yesterday, true);
    habits[0].setCompletionForDate(twoDaysAgo, false);

    // Sample data for "Drink 8 glasses of water"
    habits[1].setCompletionForDate(today, false);
    habits[1].setCompletionForDate(yesterday, true);
    habits[1].setCompletionForDate(twoDaysAgo, true);

    // Sample data for "Exercise for 30 minutes"
    habits[2].setCompletionForDate(today, false);
    habits[2].setCompletionForDate(yesterday, false);
    habits[2].setCompletionForDate(twoDaysAgo, true);
  }

  void habitDateToggled(int habitIndex, DateTime date) {
    setState(() {
      habits[habitIndex].toggleCompletionForDate(date);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("My Habits"),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // Empty function for now
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60.0),
          child: DateScroller(scrollController: _scrollController),
        ),
      ),
      body: ListView.builder(
        itemCount: habits.length,
        itemBuilder: (context, index) {
          return HabitTile(
            habit: habits[index],
            scrollController: _scrollController,
            onDateToggle: (date) => habitDateToggled(index, date),
          );
        },
      ),
    );
  }
}
