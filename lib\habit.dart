class Habit {
  final String name;
  Map<DateTime, bool> completions;

  Habit({required this.name, Map<DateTime, bool>? completions})
    : completions = completions ?? {};

  // Helper method to check if habit is completed on a specific date
  bool isCompletedOnDate(DateTime date) {
    final dateKey = DateTime(date.year, date.month, date.day);
    return completions[dateKey] ?? false;
  }

  // Helper method to toggle completion status for a specific date
  void toggleCompletionForDate(DateTime date) {
    final dateKey = DateTime(date.year, date.month, date.day);
    completions[dateKey] = !(completions[dateKey] ?? false);
  }

  // Helper method to set completion status for a specific date
  void setCompletionForDate(DateTime date, bool isCompleted) {
    final dateKey = DateTime(date.year, date.month, date.day);
    completions[dateKey] = isCompleted;
  }
}
